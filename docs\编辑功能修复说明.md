# 编辑功能修复说明

## 问题描述

演示页面的编辑按钮点击无效，提示网络错误。编辑按钮应该可以修改的字段包括：
- 内容类型
- SessionID  
- 团队标签

## 问题分析

通过代码分析发现以下问题：

### 1. 模态框缺少团队标签字段
原始模态框只有：平台手机号、平台类型、内容类型、SessionID
缺少：团队标签字段

### 2. 编辑函数字段映射错误
```javascript
// 错误的映射
platformTypeSelect.value = accountToEdit.login_type || '';  // 应该是platform
// 缺少内容类型的映射
// 缺少团队标签的映射
```

### 3. 确认函数中团队标签硬编码
```javascript
// 错误：硬编码为"-"
team_tag: '-',
```

### 4. 缺少团队标签的DOM元素引用
没有定义 `teamTagInput` 变量

## 修复方案

### 1. 添加团队标签字段到模态框
```html
<div class="form-group">
  <label for="team-tag">团队标签</label>
  <input type="text" id="team-tag" class="form-control" placeholder="请输入团队标签（可选）">
</div>
```

### 2. 添加DOM元素引用
```javascript
const teamTagInput = document.getElementById('team-tag');
```

### 3. 修复编辑函数字段映射
```javascript
// 修复后的正确映射
platformPhoneInput.value = accountToEdit.phone || '';
platformTypeSelect.value = accountToEdit.platform || '';           // 修复：platform
contentTypeSelect.value = accountToEdit.login_type || '';          // 新增：内容类型
platformSessionidInput.value = accountToEdit.sessionid || '';
teamTagInput.value = accountToEdit.team_tag || '';                 // 新增：团队标签
```

### 4. 修复确认函数
```javascript
// 获取团队标签输入值
const teamTag = teamTagInput.value.trim() || '-';

// 在账号数据中使用
const accountData = {
  // ...
  team_tag: teamTag,  // 修复：使用输入值而不是硬编码
  // ...
};
```

### 5. 修复表单清空函数
在 `showAddPlatformAccountModal()` 和 `hideAddPlatformAccountModal()` 中添加：
```javascript
teamTagInput.value = '';
```

## 修复后的功能

### 编辑流程
1. 用户点击编辑按钮
2. 弹出模态框，自动填充现有数据：
   - 平台手机号（禁用，不可修改）
   - 平台类型（显示当前值）
   - 内容类型（可修改）
   - SessionID（可修改）
   - 团队标签（可修改）
3. 用户修改需要的字段
4. 点击确认，调用PUT API更新数据
5. 成功后刷新列表

### 可编辑字段
- ✅ 内容类型：视频/文章/微头条
- ✅ SessionID：平台认证凭据
- ✅ 团队标签：自定义标签

### 不可编辑字段
- ❌ 平台手机号：作为唯一标识符
- ❌ 平台类型：头条号/百家号（通常不变）

## API调用

### 编辑模式API
```http
PUT /api/user/platform-accounts
Content-Type: application/json

{
  "phone": "***********",
  "user_id": 123,
  "is_main_account": true,
  "account_data": {
    "phone": "***********",
    "platform": "头条号",
    "login_type": "视频",        // 可修改
    "sessionid": "new_session", // 可修改
    "team_tag": "A组",          // 可修改
    "data_update_time": "2024-08-01 10:30:00",
    "login_time": "2024-08-01 10:30:00",
    "username": "-",
    "homepage_url": "-",
    "is_verified": "否",
    "drafts_count": "-",
    "account_status": "正常",
    "stats": {
      "followers": "-",
      "total_reads": "-",
      "total_income": "-",
      "yesterday_reads": "-",
      "yesterday_income": "-",
      "credit_score": "-",
      "can_withdraw_amount": "-"
    }
  },
  "sessionId": "user_session_abc123"
}
```

## 测试验证

### 测试步骤
1. 登录系统
2. 在平台账号列表中点击任意账号的"编辑"按钮
3. 验证模态框正确显示现有数据
4. 修改内容类型、SessionID、团队标签
5. 点击确认
6. 验证更新成功且列表刷新

### 预期结果
- ✅ 编辑按钮点击有效
- ✅ 模态框正确填充现有数据
- ✅ 可以修改指定字段
- ✅ 更新成功后列表刷新
- ✅ 无网络错误提示

## 权限控制

### 主账号
- 可以编辑所有平台账号

### 子账号  
- 只能编辑归属于自己的平台账号
- 权限检查：`accountToEdit.current_holder_id !== currentUser.id`

## 注意事项

1. **字段验证**：SessionID为必填字段
2. **权限检查**：编辑前验证用户权限
3. **数据同步**：编辑成功后刷新列表
4. **错误处理**：网络错误和业务错误分别处理
5. **用户体验**：编辑模式下禁用手机号字段

---

*修复完成时间: 2024-08-01*  
*修复状态: ✅ 已完成*
